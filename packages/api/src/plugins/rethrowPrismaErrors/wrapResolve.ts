import { type SchemaTypes } from "@pothos/core";
import type { GraphQLFieldResolver } from "graphql";

import { Prisma } from "../../database/__generated__/orm";
import { throwUniqueError } from "./throwUniqueError";

const wrapResolve = <Types extends SchemaTypes>(
  resolver: GraphQLFieldResolver<unknown, Types["Context"], object>,
): GraphQLFieldResolver<unknown, Types["Context"], object> => {
  return (parent, args, context, info) => {
    const result = resolver(parent, args, context, info);
    if (result instanceof Promise) {
      return result.catch((error) => {
        if (error instanceof Prisma.PrismaClientKnownRequestError) {
          if (error.code === "P2002") throwUniqueError(error);
        }
        throw error;
      });
    }
    return result;
  };
};

export { wrapResolve };
